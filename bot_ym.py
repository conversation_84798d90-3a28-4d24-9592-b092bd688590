
import subprocess
import time
import asyncio
import hashlib
import os
import shutil
import sys
import time
import platform
from threading import Thread
from pathlib import Path

from func.database import mongodb

# if not mgdb.check_mongo_service():
# mongodb.startdb()

from func.log.default_log import DefaultLog
from func.danmaku.blivedm.blivedm_core import BlivedmCore
from func.gobal.data import CommonData
from controller import sence_init

from controller.api_server import api_run
from controller.html_server import manage_run

from dotenv import load_dotenv
from src.common.logger import get_logger
from src.main import MainSystem
from src.manager.async_task_manager import async_task_manager

# 导入音乐转换服务
from func.sing.music_convert_service import music_convert_service

# 设置日志级别，减少DEBUG输出
import logging
logging.getLogger('apscheduler').setLevel(logging.WARNING)
logging.getLogger('websockets').setLevel(logging.WARNING)
logging.getLogger('urllib3').setLevel(logging.WARNING)
logging.getLogger('requests').setLevel(logging.WARNING)
# 设置MongoDB相关日志级别
logging.getLogger('pymongo').setLevel(logging.WARNING)
logging.getLogger('pymongo.command').setLevel(logging.WARNING)
logging.getLogger('pymongo.connection').setLevel(logging.WARNING)
logging.getLogger('pymongo.server').setLevel(logging.WARNING)
logging.getLogger('pymongo.topology').setLevel(logging.WARNING)

# logger = get_module_logger("main_bot")
logger = get_logger("init")

# 获取没有加载env时的环境变量
env_mask = {key: os.getenv(key) for key in os.environ}

uvicorn_server = None
driver = None
app = None
loop = None


def easter_egg():
    # 彩蛋
    from colorama import init, Fore

    init()
    text = "多年以后，面对AI行刑队，张三将会回想起他2023年在会议上讨论人工智能的那个下午"
    rainbow_colors = [Fore.RED, Fore.YELLOW, Fore.GREEN, Fore.CYAN, Fore.BLUE, Fore.MAGENTA]
    rainbow_text = ""
    for i, char in enumerate(text):
        rainbow_text += rainbow_colors[i % len(rainbow_colors)] + char
    print(rainbow_text)


def init_config():
    # 初次启动检测
    if not os.path.exists("config/bot_config.toml"):
        logger.warning("检测到bot_config.toml不存在，正在从模板复制")

        # 检查config目录是否存在
        if not os.path.exists("config"):
            os.makedirs("config")
            logger.info("创建config目录")

        shutil.copy("template/bot_config_template.toml", "config/bot_config.toml")
        logger.info("复制完成，请修改config/bot_config.toml和.env中的配置后重新启动")


def init_env():
    # 检测.env文件是否存在
    if not os.path.exists(".env"):
        logger.error("检测到.env文件不存在")
        shutil.copy("template/template.env", "./.env")
        logger.info("已从template/template.env复制创建.env，请修改配置后重新启动")


def load_env():
    # 直接加载生产环境变量配置
    if os.path.exists(".env"):
        load_dotenv(".env", override=True)
        # logger.success("成功加载环境变量配置")
        logger.info("成功加载环境变量配置")
    else:
        logger.error("未找到.env文件，请确保文件存在")
        raise FileNotFoundError("未找到.env文件，请确保文件存在")


def scan_provider(env_config: dict):
    provider = {}

    # 利用未初始化 env 时获取的 env_mask 来对新的环境变量集去重
    # 避免 GPG_KEY 这样的变量干扰检查
    env_config = dict(filter(lambda item: item[0] not in env_mask, env_config.items()))

    # 遍历 env_config 的所有键
    for key in env_config:
        # 检查键是否符合 {provider}_BASE_URL 或 {provider}_KEY 的格式
        if key.endswith("_BASE_URL") or key.endswith("_KEY"):
            # 提取 provider 名称
            provider_name = key.split("_", 1)[0]  # 从左分割一次，取第一部分

            # 初始化 provider 的字典（如果尚未初始化）
            if provider_name not in provider:
                provider[provider_name] = {"url": None, "key": None}

            # 根据键的类型填充 url 或 key
            if key.endswith("_BASE_URL"):
                provider[provider_name]["url"] = env_config[key]
            elif key.endswith("_KEY"):
                provider[provider_name]["key"] = env_config[key]

    # 检查每个 provider 是否同时存在 url 和 key
    for provider_name, config in provider.items():
        if config["url"] is None or config["key"] is None:
            logger.error(f"provider 内容：{config}\nenv_config 内容：{env_config}")
            raise ValueError(f"请检查 '{provider_name}' 提供商配置是否丢失 BASE_URL 或 KEY 环境变量")


async def graceful_shutdown():
    try:
        logger.info("正在优雅关闭麦麦...")

        # 停止所有异步任务
        await async_task_manager.stop_and_wait_all_tasks()

        # 获取所有剩余任务，排除当前任务
        remaining_tasks = [t for t in asyncio.all_tasks() if t is not asyncio.current_task()]

        if remaining_tasks:
            logger.info(f"正在取消 {len(remaining_tasks)} 个剩余任务...")

            # 取消所有剩余任务
            for task in remaining_tasks:
                if not task.done():
                    task.cancel()

            # 等待所有任务完成，设置超时
            try:
                await asyncio.wait_for(asyncio.gather(*remaining_tasks, return_exceptions=True), timeout=15.0)
                logger.info("所有剩余任务已成功取消")
            except asyncio.TimeoutError:
                logger.warning("等待任务取消超时，强制继续关闭")
            except Exception as e:
                logger.error(f"等待任务取消时发生异常: {e}")

        logger.info("麦麦优雅关闭完成")

    except Exception as e:
        logger.error(f"麦麦关闭失败: {e}")


def check_eula():
    eula_confirm_file = Path("eula.confirmed")
    privacy_confirm_file = Path("privacy.confirmed")
    eula_file = Path("EULA.md")
    privacy_file = Path("PRIVACY.md")

    eula_updated = True
    eula_new_hash = None
    privacy_updated = True
    privacy_new_hash = None

    eula_confirmed = False
    privacy_confirmed = False

    # 首先计算当前EULA文件的哈希值
    if eula_file.exists():
        with open(eula_file, "r", encoding="utf-8") as f:
            eula_content = f.read()
        eula_new_hash = hashlib.md5(eula_content.encode("utf-8")).hexdigest()
    else:
        logger.error("EULA.md 文件不存在")
        raise FileNotFoundError("EULA.md 文件不存在")

    # 首先计算当前隐私条款文件的哈希值
    if privacy_file.exists():
        with open(privacy_file, "r", encoding="utf-8") as f:
            privacy_content = f.read()
        privacy_new_hash = hashlib.md5(privacy_content.encode("utf-8")).hexdigest()
    else:
        logger.error("PRIVACY.md 文件不存在")
        raise FileNotFoundError("PRIVACY.md 文件不存在")

    # 检查EULA确认文件是否存在
    if eula_confirm_file.exists():
        with open(eula_confirm_file, "r", encoding="utf-8") as f:
            confirmed_content = f.read()
        if eula_new_hash == confirmed_content:
            eula_confirmed = True
            eula_updated = False
    if eula_new_hash == os.getenv("EULA_AGREE"):
        eula_confirmed = True
        eula_updated = False

    # 检查隐私条款确认文件是否存在
    if privacy_confirm_file.exists():
        with open(privacy_confirm_file, "r", encoding="utf-8") as f:
            confirmed_content = f.read()
        if privacy_new_hash == confirmed_content:
            privacy_confirmed = True
            privacy_updated = False
    if privacy_new_hash == os.getenv("PRIVACY_AGREE"):
        privacy_confirmed = True
        privacy_updated = False

def raw_main():
    # 利用 TZ 环境变量设定程序工作的时区
    if platform.system().lower() != "windows":
        time.tzset()

    check_eula()
    print("检查EULA和隐私条款完成")
    easter_egg()
    init_config()
    init_env()
    load_env()

    env_config = {key: os.getenv(key) for key in os.environ}
    scan_provider(env_config)

    # 返回MainSystem实例
    return MainSystem()

def run_ym():
    # 设置控制台日志
    log = DefaultLog().getLogger()
    # 重定向print输出到日志文件
    def print(*args, **kwargs):
        log.info(*args, **kwargs)

    commonData = CommonData()  #基础数据
    Ai_Name = commonData.Ai_Name  # Ai名称

    log.info("=======================================================")
    log.info(f"开始启动人工智能【{Ai_Name}】！")
    # 1.b站直播间 2.api web
    mode = commonData.mode
    # ============= B站直播间 =====================
    blivedmCore = BlivedmCore()
    # ============================================
    log.info("--------------------")
    log.info("AI虚拟主播-启动成功！")
    log.info("--------------------")
    log.info("=======================================================")

    sence_init.senceInit()

    # 开启web
    app_thread = Thread(target = api_run)
    app_thread.start()

    app_thread = Thread(target = manage_run)
    app_thread.start()

    # 启动音乐转换服务
    try:
        music_convert_service.start_service(host="127.0.0.1", port=17171)
        log.info("音乐转换服务启动成功")
    except Exception as e:
        log.error(f"音乐转换服务启动失败: {e}")

    # 方案一：
    # asyncio.run(blivedmCore.listen_blivedm_task())
    # 方案二（修复版）：在新线程中创建事件循环来运行异步任务
    def run_blivedm_task():
        # 为新线程创建独立的事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(blivedmCore.listen_blivedm_task())
        except Exception as e:
            log.error(f"B站直播间监听异常: {e}")
        finally:
            loop.close()
    
    bili_thread = Thread(target=run_blivedm_task)
    bili_thread.start()
    # 方案三：
    # blivedmCore.listen_blivedm_task()

def run():
    try:
        run_ym()

        print("run-------bot---------------")
        # 获取MainSystem实例
        main_system = raw_main()
        
        # 创建事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            # 执行初始化和任务调度
            loop.run_until_complete(main_system.initialize())
            loop.run_until_complete(main_system.schedule_tasks())
        except KeyboardInterrupt:
            # loop.run_until_complete(global_api.stop())
            logger.warning("收到中断信号，正在优雅关闭...")
            loop.run_until_complete(graceful_shutdown())
        finally:
            loop.close()

    except Exception as e:
        logger.error(f"主程序异常: {str(e)}")
        if loop and not loop.is_closed():
            loop.run_until_complete(graceful_shutdown())
            loop.close()
        sys.exit(1)

if __name__ == "__main__":
 
    run()
